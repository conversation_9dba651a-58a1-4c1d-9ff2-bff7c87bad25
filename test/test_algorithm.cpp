#include "libSCR_5000_Alg.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <chrono>
#include <cstring>
#include <iomanip>
#include <algorithm>
#include <dirent.h>
#include <filesystem>

// 常量定义
const int ROWS = 1024;
const int COLS = 2048;

bool compareFilesByNumber(const std::string& a, const std::string& b) {
    // 提取文件名（去掉路径和扩展名）
    auto extractBaseName = [](const std::string& path) {
        size_t lastSlash = path.find_last_of("/\\");
        size_t lastDot = path.find_last_of('.');
        if (lastDot == std::string::npos) lastDot = path.size();
        return path.substr(lastSlash + 1, lastDot - lastSlash - 1);
    };

    std::string baseA = extractBaseName(a);
    std::string baseB = extractBaseName(b);

    // 按 '_' 分割字符串，逐个比较数字部分
    size_t posA = 0, posB = 0;
    while (posA < baseA.size() && posB < baseB.size()) {
        // 提取数字部分
        auto getNumber = [](const std::string& s, size_t& pos) {
            long num = 0;
            while (pos < s.size() && isdigit(s[pos])) {
                num = num * 10 + (s[pos] - '0');
                pos++;
            }
            if (pos < s.size() && s[pos] == '_') pos++; // 跳过分隔符
            return num;
        };

        long numA = getNumber(baseA, posA);
        long numB = getNumber(baseB, posB);

        if (numA != numB) {
            return numA < numB;
        }
    }

    // 如果前缀相同，按字符串长度排序（可选）
    return baseA.size() < baseB.size();
}

std::vector<std::string> get_bin_files(const std::string& folder_path) {
    std::vector<std::string> bin_files;
    DIR* dir = opendir(folder_path.c_str());
    if (!dir) {
        std::cerr << "Cannot open directory: " << folder_path << std::endl;
        return bin_files;
    }
    dirent* ent;
    while ((ent = readdir(dir))) {
        std::string name(ent->d_name);
        if (name.size() > 4 && name.substr(name.size() - 4) == ".bin")
            bin_files.push_back(folder_path + "/" + name);
    }
    closedir(dir);
    std::sort(bin_files.begin(), bin_files.end(), compareFilesByNumber);
    return bin_files;
}

// 模拟读取连续3帧雷达数据的函数
bool LoadRadarDataFromFile(const std::vector<std::string>& file_paths,
                           std::vector<char>& merged_bytes,
                           char** input_head_data,
                           char** input_data,
                           size_t* input_data_size)
{
    if (file_paths.size() != 3) return false;

    constexpr size_t S_head_bytes = 1024 * 40;                // 40960
    constexpr size_t per_frame_head = S_head_bytes * 2;       // S+D heads for one frame
    constexpr size_t S_data_bytes = 1024 * 2048 * 2 * sizeof(float); // 16777216
    constexpr size_t per_frame_data = S_data_bytes * 2;       // S+D data for one frame

    std::vector<char> headers; headers.reserve(per_frame_head * 3);
    std::vector<char> datas;   datas.reserve(per_frame_data * 3);

    for (const auto& file_path : file_paths) {
        std::ifstream file(file_path, std::ios::binary | std::ios::ate);
        if (!file) return false;
        std::streamsize sz = file.tellg();
        file.seekg(0, std::ios::beg);
        if ((size_t)sz < per_frame_head + per_frame_data) {
            std::cerr << "文件尺寸小于预期: " << file_path << std::endl;
            return false;
        }
        std::vector<char> tmp((size_t)sz);
        if (!file.read(tmp.data(), sz)) return false;

        // 拆分并追加到 headers / datas
        headers.insert(headers.end(), tmp.data(), tmp.data() + per_frame_head);
        datas.insert(datas.end(), tmp.data() + per_frame_head, tmp.data() + per_frame_head + per_frame_data);
    }

    // 最终合并为： [ all headers ][ all datas ]
    merged_bytes.clear();
    merged_bytes.insert(merged_bytes.end(), headers.begin(), headers.end());
    merged_bytes.insert(merged_bytes.end(), datas.begin(), datas.end());

    *input_head_data = merged_bytes.data();                       // 指向 headers 开头
    *input_data      = merged_bytes.data() + headers.size();      // 指向 datas 开头
    *input_data_size = datas.size();
    return true;
}



// 打印检测结果
void PrintDetectionResults(const DetectionResult* results, int num_results) {
    std::cout << "\n=== 轻量化检测结果 ===" << std::endl;
    std::cout << "检测到 " << num_results << " 个目标中心点" << std::endl;

    if (num_results > 0) {
        std::cout << std::left;
        std::cout << std::setw(8) << "num"
                  << std::setw(10) << "行坐标"
                  << std::setw(10) << "列坐标"
                  << std::setw(8) << "frame" << std::endl;

        for (int i = 0; i < std::min(num_results, 10); ++i) {  // 最多显示10个
            const auto& result = results[i];
            std::cout << std::setw(8) << i+1
                      << std::setw(10) << result.row
                      << std::setw(10) << result.col
                      << std::setw(8) << result.frame << std::endl;
        }

        if (num_results > 10) {
            std::cout << "... 还有 " << (num_results - 10) << " 个结果未显示" << std::endl;
        }
    }
}

// 打印跟踪结果
void PrintTrackingResults(const TrackingResult* results, int num_results) {
    if (!std::cout.good()) return;   // 输出端已关闭，直接返回

    std::cout << "\n=== 跟踪结果 ===\n";
    std::cout << "跟踪到 " << num_results << " 个目标\n";

    if (num_results <= 0) return;

    // 设置格式后一次性输出，减少 flush 次数
    std::ios::fmtflags f(std::cout.flags());
    std::cout << std::left << std::fixed << std::setprecision(2);
    std::cout << std::setw(8)  << "ID"
              << std::setw(10) << "X(m)"
              << std::setw(10) << "Y(m)"
              << std::setw(10) << "Z(m)"
              << std::setw(10) << "Vx(m/s)"
              << std::setw(10) << "Vy(m/s)"
              << std::setw(10) << "Vz(m/s)"
              << std::setw(10) << "距离(m)"
              << std::setw(10) << "方位(°)"
              << std::setw(10) << "俯仰(°)\n";

    for (int i = 0; i < num_results; ++i) {
        const auto& r = results[i];
        std::cout << std::setw(8)  << r.id
                  << std::setw(10) << r.x
                  << std::setw(10) << r.y
                  << std::setw(10) << r.z
                  << std::setw(10) << r.vx
                  << std::setw(10) << r.vy
                  << std::setw(10) << r.vz
                  << std::setw(10) << r.fMR
                  << std::setw(10) << r.fMA
                  << std::setw(10) << r.fME << '\n';
    }
    std::cout.flush();
    if (!std::cout.good()) { /* 可选：记录日志 */ }

    std::cout.flags(f);  // 恢复原格式
}

int main() {
    std::cout << "=== SCR_5000算法库测试程序 ===" << std::endl;

    // 获取版本信息
    AlgorithmVersion version;
    GetVersionInfo(&version);
    std::cout << "算法库版本: " << version.version_string << std::endl;
    std::cout << "构建时间: " << version.build_date << std::endl;

    // 算法库初始化
    std::cout << "\n开始初始化算法库..." << std::endl;
    int init_status = InitializeAlgorithmLibrary("config/custom_config.json");
    if (init_status != 0) {
        std::cerr << "算法库初始化失败，错误码: " << init_status << std::endl;
        return -1;
    }

    // 测试数据目录路径
    std::string test_data_dir = "data/test1";

    std::cout << "\n正在扫描测试数据目录: " << test_data_dir << std::endl;

    // 获取所有bin文件
    auto bin_files = get_bin_files(test_data_dir);
    if (bin_files.empty()) {
        std::cerr << "未找到任何bin文件" << std::endl;
        return -1;
    }

    std::cout << "找到 " << bin_files.size() << " 个bin文件" << std::endl;

    // 滑动窗口，每次 3 个连续文件
    const size_t window = 3;
    if (bin_files.size() < window) {
        std::cerr << "文件数量不足 3 个，无法形成任何窗口" << std::endl;
        return -1;
    }

    // 控制处理的组数
    size_t max_groups = 20; // 最多处理 20 组数据，可以根据需要修改
    size_t num_groups = std::max(max_groups, bin_files.size());

    for (size_t group_idx = 0; group_idx < num_groups; ++group_idx) {
        std::cout << "\n=== 处理滑动窗口 [" << (group_idx + 1) << "] ===" << std::endl;

        std::vector<std::string> current_group;
        for (size_t i = 0; i < window; ++i) {
            size_t file_idx = (group_idx + i) % bin_files.size();
            current_group.push_back(bin_files[file_idx]);
            std::cout << "文件 " << (i + 1) << ": "
                      << std::filesystem::path(bin_files[file_idx]).filename() << std::endl;
        }

        // 加载雷达数据
        std::vector<char> radar_data; // 合并后的数据
        char* input_head_data = nullptr; // 帧头数据
        char* input_data = nullptr; // 帧数据
        size_t input_data_size = 0;

        if (!LoadRadarDataFromFile(current_group, radar_data, &input_head_data, &input_data, &input_data_size)) {
            std::cerr << "加载第 " << (group_idx + 1) << " 组雷达数据失败" << std::endl;
            continue;
        }

        // 执行目标检测
        std::cout << "\n开始执行目标检测..." << std::endl;
        DetectionResult* detection_results = nullptr;
        int num_detections = 0;
        const FrameHeader_Alg* S_head = nullptr;
        ColumnSegmentData* column_segments = nullptr;
        int num_segments = 0;

        auto start_time = std::chrono::high_resolution_clock::now();
        int detection_status = TargetDetection(input_head_data, input_data, &detection_results, &num_detections, &S_head, &column_segments, &num_segments);
        auto end_time = std::chrono::high_resolution_clock::now();

        auto detection_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        std::cout << "目标检测耗时: " << detection_duration.count() << " ms" << std::endl;

        if (detection_status != 0) {
            std::cerr << "目标检测失败，错误码: " << detection_status << std::endl;
            continue;
        }

        PrintDetectionResults(detection_results, num_detections);

        // 执行目标跟踪
        if (num_detections >= 0) {
            std::cout << "\n开始执行目标跟踪..." << std::endl;
            TrackingResult* tracking_results = nullptr;
            int num_tracks = 0;

            start_time = std::chrono::high_resolution_clock::now();
            TargetTracking(detection_results, num_detections, S_head, column_segments, num_segments, &tracking_results, &num_tracks, [](const TrackingResult* batch, int num_results)
            {
                // for (int i = 0; i < num_results; ++i) {
                //     const auto& result = batch[i];
                //     std::cout << "ID: " << result.id << std::endl;
                // }
                PrintTrackingResults(batch, num_results);
            });
            end_time = std::chrono::high_resolution_clock::now();

            auto tracking_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            std::cout << "目标跟踪耗时: " << tracking_duration.count() << " ms" << std::endl;

            // PrintTrackingResults(tracking_results, num_tracks);

            // 释放跟踪结果内存
            if (tracking_results) {
                ReleaseTrackingResults(tracking_results);
            }
        }

        // 释放列数据段内存
        if (column_segments) {
            for (int i = 0; i < num_segments; ++i) {
                delete[] column_segments[i].data_S;
                delete[] column_segments[i].data_D;
            }
            delete[] column_segments;
        }

        // 释放检测结果内存
        if (detection_results) {
            ReleaseDetectionResults(detection_results);
        }

        std::cout << "第 " << (group_idx + 1) << " 组处理完成" << std::endl;
    }

    // 释放所有资源
    std::cout << "\n释放算法库资源..." << std::endl;
    ReleaseAllResources();

    std::cout << "\n所有测试完成！" << std::endl;
    return 0;
}