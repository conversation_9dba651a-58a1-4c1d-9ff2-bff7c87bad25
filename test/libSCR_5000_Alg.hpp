#pragma once
#include <vector>
#include <cstdint>
#include <cstddef>
#include <functional>

#define ALGORITHM_NAME "SCR_5000"
#define ALGORITHM_API __attribute__ ((visibility ("default")))

// 版本信息结构体
typedef struct {
    int major;
    int minor;
    int patch;
    const char* version_string;
    const char* build_date;
} AlgorithmVersion;

// 帧头结构定义
typedef struct {
    uint32_t marker;
    uint16_t channel;
    uint16_t circle_num;
    uint16_t frame_num;
    uint8_t  task_type;
    uint8_t  group_id;
    uint16_t group_limit;
    uint16_t pulse_id;
    uint8_t  waveform;
    uint8_t  waveform_type;
    uint16_t frame_length;
    uint32_t angle; // 方位角*100
    uint16_t angle_counter;
    char     reserved[14];
} FrameHeader_Alg;

// 单帧数据视图
typedef struct {
    const FrameHeader_Alg* head_S;
    const FrameHeader_Alg* head_D;
    const float* data_S;
    const float* data_D;
} FrameDataView;

// 目标检测结果结构体
typedef struct {
    int row;                              // 原始行坐标（对应检测图像的y坐标）
    int col;                              // 原始列坐标（对应检测图像的x坐标）
    uint16_t frame;                       // 帧号
} DetectionResult;

// 列数据段结构（用于FFT处理）
typedef struct {
    float* data_S;                       // 和通道列数据（复数格式：实部虚部交替）
    float* data_D;                       // 差通道列数据（复数格式：实部虚部交替）
    int segment_length;                  // 段长度
    int segment_start;                   // 段起始位置
    int col;                             // 列号
    int row;                             // 行号
} ColumnSegmentData;

// 跟踪结果结构体
typedef struct {
    unsigned int id;                      // 目标id
    float vx;                             // x方向速度
    float vy;                             // y方向速度
    float vz;                             // z方向速度
    float x;                              // x坐标
    float y;                              // y坐标
    float z;                              // z坐标
    float fMV;                            // 径向速度
    float fMR;                            // 径向距离
    float fMA;                            // 目标方位角
    float fME;                            // 目标俯仰角
    float fSNR;                           // 信噪比
    float fEn;                            // 峰值点能量
    float fRcs;                           // rcs
    unsigned int type;                    // 目标类型
    unsigned long long FPGATimeLog;       // fpga时间戳
    int PreShow;                          // 上位机轨迹预测值是否显示标识：1：显示， 2：不显示
} TrackingResult;

// 版本接口函数
ALGORITHM_API void GetVersionInfo(AlgorithmVersion* version_info);

// 算法库初始化
ALGORITHM_API int InitializeAlgorithmLibrary(const char* config_path);

// ==================== 目标检测算法 ====================
ALGORITHM_API int TargetDetection(
    char* input_head_data,               // 输入帧头数据
    char* input_data,                    // 输入雷达数据
    DetectionResult** detection_results, // 输出检测结果数组
    int* num_detections,                 // 输出检测结果数量
    const FrameHeader_Alg** S_head,      // 输出S通道帧头指针
    ColumnSegmentData** column_segments, // 输出提取的列数据段
    int* num_segments                    // 输出列数据段数量
);

using BatchCb = std::function<void(const TrackingResult*, int)>;

// ==================== 目标跟踪算法 ====================
ALGORITHM_API void TargetTracking(
    const DetectionResult* detection_results, // 输入检测结果
    int num_detections,                  // 输入检测结果数量
    const FrameHeader_Alg* S_head,       // 输入S通道帧头
    const ColumnSegmentData* column_segments, // 输入列数据段
    int num_segments,                    // 输入列数据段数量
    TrackingResult** tracking_results,   // 输出跟踪结果数组
    int* num_tracks,                     // 输出跟踪结果数量
    BatchCb batch_cb = nullptr
);

// 资源释放函数
ALGORITHM_API void ReleaseDetectionResults(DetectionResult* detection_results);
ALGORITHM_API void ReleaseTrackingResults(TrackingResult* tracking_results);
ALGORITHM_API void ReleaseAllResources();